// Hockey Equipment Inventory Browser App
class InventoryApp {
    constructor() {
        this.inventory = [];
        this.filteredItems = [];
        this.currentPage = 1;
        this.itemsPerPage = 24;
        this.sortBy = 'name-asc';
        this.filters = {
            search: '',
            minPrice: '',
            maxPrice: '',
            brands: [],
            categories: [],
            stockStatus: 'all'
        };
        
        // Cart functionality
        this.cart = [];
        this.isCartOpen = false;
        
        this.searchTimeout = null;
        this.init();
    }

    async init() {
        try {
            await this.loadInventoryData();
            this.setupEventListeners();
            this.setupCartEventListeners();
            this.setupNavigationEventListeners();
            this.initializeLightMode(); // Force light mode only
            this.loadUserPreferences();
            this.loadCartFromStorage();
            this.loadURLState();
            this.populateFilterOptions();
            this.updateDropdownLabels(); // Initialize dropdown labels
            this.applyFilters();
            this.setupKeyboardShortcuts();
            this.updateCartUI();
            this.initializeAccessibilityFeatures();
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Failed to load inventory data. Please refresh the page.');
        }
    }

    async loadInventoryData() {
        try {
            const response = await fetch('inventory_data.json');
            const data = await response.json();
            this.inventory = data.inventory || [];
            this.brands = [...new Set(this.inventory.map(item => item.brand))].sort();
            this.categories = [...new Set(this.inventory.map(item => item.category))].sort();
            
            // Hide loading state
            document.getElementById('loadingState').style.display = 'none';
        } catch (error) {
            console.error('Error loading inventory data:', error);
            // Fallback to expanded sample data if API fails
            this.loadFallbackData();
        }
    }

    /**
     * Fallback function when main inventory data fails to load
     * Sets empty inventory and shows error state instead of sample data
     */
    loadFallbackData() {
        // Set empty inventory instead of using sample data
        this.inventory = [];
        this.brands = [];
        this.categories = [];
        
        // Hide loading state and show error message
        document.getElementById('loadingState').style.display = 'none';
        this.showError('Unable to load inventory data. Please check your connection and try refreshing the page.');
    }

    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.filters.search = e.target.value.toLowerCase();
                this.currentPage = 1; // Reset to first page when filtering
                this.applyFilters();
                this.updateURL();
            }, 300);
        });

        // Price range inputs
        document.getElementById('minPrice').addEventListener('input', (e) => {
            this.filters.minPrice = e.target.value;
            this.currentPage = 1;
            this.applyFilters();
            this.updateURL();
        });

        document.getElementById('maxPrice').addEventListener('input', (e) => {
            this.filters.maxPrice = e.target.value;
            this.currentPage = 1;
            this.applyFilters();
            this.updateURL();
        });

        // Stock status radio buttons
        document.querySelectorAll('input[name="stockStatus"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.filters.stockStatus = e.target.value;
                this.currentPage = 1;
                this.applyFilters();
                this.updateURL();
            });
        });

        // Sort and items per page
        document.getElementById('sortSelect').addEventListener('change', (e) => {
            this.sortBy = e.target.value;
            this.currentPage = 1;
            this.applyFilters();
            this.updateURL();
            this.saveUserPreferences();
        });

        document.getElementById('itemsPerPage').addEventListener('change', (e) => {
            this.itemsPerPage = e.target.value === 'all' ? 'all' : parseInt(e.target.value);
            this.currentPage = 1;
            this.applyFilters();
            this.updateURL();
            this.saveUserPreferences();
        });

        // Clear filters button
        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearAllFilters();
        });

        // Dropdown toggles
        this.setupDropdownToggle('brandDropdown', 'brandMenu');
        this.setupDropdownToggle('categoryDropdown', 'categoryMenu');

        // Dropdown searches
        this.setupDropdownSearch('brandSearch', 'brandOptions');
        this.setupDropdownSearch('categorySearch', 'categoryOptions');

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown-filter')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
                document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
                    toggle.classList.remove('active');
                });
            }
        });
    }

    setupDropdownToggle(toggleId, menuId) {
        const toggle = document.getElementById(toggleId);
        const menu = document.getElementById(menuId);
        
        toggle.addEventListener('click', (e) => {
            e.stopPropagation();
            const isOpen = menu.classList.contains('show');
            
            // Close all dropdowns first
            document.querySelectorAll('.dropdown-menu').forEach(m => m.classList.remove('show'));
            document.querySelectorAll('.dropdown-toggle').forEach(t => t.classList.remove('active'));
            
            // Toggle current dropdown
            if (!isOpen) {
                menu.classList.add('show');
                toggle.classList.add('active');
            }
        });
    }

    setupDropdownSearch(searchId, optionsId) {
        const searchInput = document.getElementById(searchId);
        const optionsContainer = document.getElementById(optionsId);
        
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const options = optionsContainer.querySelectorAll('.dropdown-option');
            
            options.forEach(option => {
                const text = option.textContent.toLowerCase();
                option.style.display = text.includes(searchTerm) ? 'flex' : 'none';
            });
        });
    }

    populateFilterOptions() {
        this.populateBrandOptions();
        this.populateCategoryOptions();
    }

    populateBrandOptions() {
        const container = document.getElementById('brandOptions');
        container.innerHTML = '';
        
        this.brands.forEach(brand => {
            const option = document.createElement('div');
            option.className = 'dropdown-option';
            option.innerHTML = `
                <input type="checkbox" id="brand-${brand}" value="${brand}">
                <label for="brand-${brand}">${brand}</label>
            `;
            
            const checkbox = option.querySelector('input');
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.filters.brands.push(brand);
                } else {
                    this.filters.brands = this.filters.brands.filter(b => b !== brand);
                }
                this.updateDropdownLabel('brandDropdown', 'brands', 'Select Brands');
                this.currentPage = 1;
                this.applyFilters();
                this.updateURL();
            });
            
            container.appendChild(option);
        });
    }

    populateCategoryOptions() {
        const container = document.getElementById('categoryOptions');
        container.innerHTML = '';
        
        this.categories.forEach(category => {
            const option = document.createElement('div');
            option.className = 'dropdown-option';
            option.innerHTML = `
                <input type="checkbox" id="category-${category.replace(/\s+/g, '-')}" value="${category}">
                <label for="category-${category.replace(/\s+/g, '-')}">${category}</label>
            `;
            
            const checkbox = option.querySelector('input');
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.filters.categories.push(category);
                } else {
                    this.filters.categories = this.filters.categories.filter(c => c !== category);
                }
                this.updateDropdownLabel('categoryDropdown', 'categories', 'Select Categories');
                this.currentPage = 1;
                this.applyFilters();
                this.updateURL();
            });
            
            container.appendChild(option);
        });
    }

    updateDropdownLabel(dropdownId, filterKey, defaultText) {
        const dropdown = document.getElementById(dropdownId);
        const span = dropdown.querySelector('span');
        const count = this.filters[filterKey].length;
        
        if (count === 0) {
            span.textContent = defaultText;
        } else if (count === 1) {
            span.textContent = this.filters[filterKey][0];
        } else {
            span.textContent = `${count} selected`;
        }
    }

    updateDropdownLabels() {
        this.updateDropdownLabel('brandDropdown', 'brands', 'Select Brands');
        this.updateDropdownLabel('categoryDropdown', 'categories', 'Select Categories');
    }

    applyFilters() {
        this.filteredItems = this.inventory.filter(item => {
            // Search filter
            if (this.filters.search && !item.name.toLowerCase().includes(this.filters.search)) {
                return false;
            }

            // Price range filter
            if (this.filters.minPrice && item.priceNumeric < parseFloat(this.filters.minPrice)) {
                return false;
            }
            if (this.filters.maxPrice && item.priceNumeric > parseFloat(this.filters.maxPrice)) {
                return false;
            }

            // Brand filter
            if (this.filters.brands.length > 0 && !this.filters.brands.includes(item.brand)) {
                return false;
            }

            // Category filter
            if (this.filters.categories.length > 0 && !this.filters.categories.includes(item.category)) {
                return false;
            }

        // Always exclude out-of-stock items (stock <= 0)
        if (item.stock <= 0) {
            return false;
        }

        // Stock status filter
        switch (this.filters.stockStatus) {
            case 'inStock':
                return true; // All remaining items are in stock
            case 'lowStock':
                return item.stock <= 5;
            default: // 'all'
                return true;
        }
        });

        this.sortItems();
        this.updateCounters();
        this.renderItems();
        this.renderPagination();
    }

    sortItems() {
        this.filteredItems.sort((a, b) => {
            switch (this.sortBy) {
                case 'name-asc':
                    return a.name.localeCompare(b.name);
                case 'name-desc':
                    return b.name.localeCompare(a.name);
                case 'price-asc':
                    return a.priceNumeric - b.priceNumeric;
                case 'price-desc':
                    return b.priceNumeric - a.priceNumeric;
                case 'stock-desc':
                    return b.stock - a.stock;
                default:
                    return 0;
            }
        });
    }

    updateCounters() {
        document.getElementById('itemCounter').textContent = `${this.inventory.length} items`;
        const countText = `Showing ${this.filteredItems.length} of ${this.inventory.length} items`;
        document.getElementById('resultsCounter').textContent = countText;
        
        // Announce filter results to screen readers
        this.announceToScreenReader(`${this.filteredItems.length} item${this.filteredItems.length !== 1 ? 's' : ''} matching your filters`);
    }

    renderItems() {
        const grid = document.getElementById('inventoryGrid');
        const noResults = document.getElementById('noResults');
        
        if (this.filteredItems.length === 0) {
            grid.innerHTML = '';
            noResults.style.display = 'block';
            return;
        }
        
        noResults.style.display = 'none';
        
        const itemsToShow = this.getItemsForCurrentPage();
        
        grid.innerHTML = itemsToShow.map(item => `
            <div class="inventory-card">
                <div class="card-header">
                    <h3 class="card-title">${item.name}</h3>
                </div>
                <div class="card-badges">
                    <span class="badge badge--brand">${item.brand}</span>
                    <span class="badge badge--category">${item.category}</span>
                </div>
                <div class="card-system-id">System ID: ${item.id}</div>
                <div class="card-price">${item.price}</div>
                <div class="card-stock">
                    <span class="stock-level">Stock: ${item.stock}</span>
                    <span class="stock-badge ${this.getStockBadgeClass(item.stock)}">
                        ${this.getStockBadgeText(item.stock)}
                    </span>
                </div>
                <button class="add-to-cart-btn" 
                        onclick="app.addToCart(${item.id})" 
                        ${item.stock === 0 ? 'disabled' : ''}>
                    ${item.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
                </button>
            </div>
        `).join('');
    }

    getItemsForCurrentPage() {
        if (this.itemsPerPage === 'all') {
            return this.filteredItems;
        }
        
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        return this.filteredItems.slice(startIndex, endIndex);
    }

    getStockBadgeClass(stock) {
        if (stock === 0) return 'stock-badge--out-of-stock';
        if (stock <= 4) return 'stock-badge--very-low';
        if (stock <= 10) return 'stock-badge--low-stock';
        return 'stock-badge--in-stock';
    }

    getStockBadgeText(stock) {
        if (stock === 0) return 'Out of Stock';
        if (stock <= 4) return 'Very Low';
        if (stock <= 10) return 'Low Stock';
        return 'In Stock';
    }

    renderPagination() {
        const container = document.getElementById('paginationContainer');
        
        if (this.itemsPerPage === 'all' || this.filteredItems.length <= this.itemsPerPage) {
            container.style.display = 'none';
            return;
        }
        
        container.style.display = 'flex';
        const pagination = document.getElementById('pagination');
        const totalPages = Math.ceil(this.filteredItems.length / this.itemsPerPage);
        
        // Ensure current page is within bounds
        if (this.currentPage > totalPages) {
            this.currentPage = Math.max(1, totalPages);
        }
        
        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === 1 ? 'disabled' : ''}" 
                    ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="app.goToPage(${this.currentPage - 1})">
                ‹ Previous
            </button>
        `;
        
        // Page numbers with improved logic
        const maxVisiblePages = 5;
        let startPage, endPage;
        
        if (totalPages <= maxVisiblePages) {
            startPage = 1;
            endPage = totalPages;
        } else {
            const halfVisible = Math.floor(maxVisiblePages / 2);
            if (this.currentPage <= halfVisible) {
                startPage = 1;
                endPage = maxVisiblePages;
            } else if (this.currentPage + halfVisible >= totalPages) {
                startPage = totalPages - maxVisiblePages + 1;
                endPage = totalPages;
            } else {
                startPage = this.currentPage - halfVisible;
                endPage = this.currentPage + halfVisible;
            }
        }
        
        // Add first page and ellipsis if needed
        if (startPage > 1) {
            paginationHTML += `<button class="pagination-btn" onclick="app.goToPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            }
        }
        
        // Add visible page numbers
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                        onclick="app.goToPage(${i})">${i}</button>
            `;
        }
        
        // Add ellipsis and last page if needed
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            }
            paginationHTML += `<button class="pagination-btn" onclick="app.goToPage(${totalPages})">${totalPages}</button>`;
        }
        
        // Next button
        paginationHTML += `
            <button class="pagination-btn ${this.currentPage === totalPages ? 'disabled' : ''}" 
                    ${this.currentPage === totalPages ? 'disabled' : ''} 
                    onclick="app.goToPage(${this.currentPage + 1})">
                Next ›
            </button>
        `;
        
        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredItems.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderItems();
            this.renderPagination();
            this.updateURL();
            
            // Scroll to top of content
            document.querySelector('.content-area').scrollIntoView({ behavior: 'smooth' });
        }
    }

    clearAllFilters() {
        // Reset all filters
        this.filters = {
            search: '',
            minPrice: '',
            maxPrice: '',
            brands: [],
            categories: [],
            stockStatus: 'all'
        };
        
        // Reset form inputs
        document.getElementById('searchInput').value = '';
        document.getElementById('minPrice').value = '';
        document.getElementById('maxPrice').value = '';
        document.querySelector('input[name="stockStatus"][value="all"]').checked = true;
        
        // Reset dropdown checkboxes
        document.querySelectorAll('#brandOptions input[type="checkbox"]').forEach(cb => cb.checked = false);
        document.querySelectorAll('#categoryOptions input[type="checkbox"]').forEach(cb => cb.checked = false);
        
        // Reset dropdown labels
        this.updateDropdownLabels();
        
        // Reset page
        this.currentPage = 1;
        
        // Apply filters and update URL
        this.applyFilters();
        this.updateURL();
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+F or Cmd+F to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                document.getElementById('searchInput').focus();
            }
            
            // Escape to clear search
            if (e.key === 'Escape') {
                const searchInput = document.getElementById('searchInput');
                if (document.activeElement === searchInput && searchInput.value) {
                    searchInput.value = '';
                    this.filters.search = '';
                    this.currentPage = 1;
                    this.applyFilters();
                    this.updateURL();
                }
            }
        });
    }

    updateURL() {
        const params = new URLSearchParams();
        
        if (this.filters.search) params.set('search', this.filters.search);
        if (this.filters.minPrice) params.set('minPrice', this.filters.minPrice);
        if (this.filters.maxPrice) params.set('maxPrice', this.filters.maxPrice);
        if (this.filters.brands.length > 0) params.set('brands', this.filters.brands.join(','));
        if (this.filters.categories.length > 0) params.set('categories', this.filters.categories.join(','));
        if (this.filters.stockStatus !== 'all') params.set('stockStatus', this.filters.stockStatus);
        if (this.sortBy !== 'name-asc') params.set('sort', this.sortBy);
        if (this.itemsPerPage !== 24) params.set('itemsPerPage', this.itemsPerPage);
        if (this.currentPage !== 1) params.set('page', this.currentPage);
        
        const url = params.toString() ? `${window.location.pathname}?${params.toString()}` : window.location.pathname;
        window.history.replaceState({}, '', url);
    }

    loadURLState() {
        const params = new URLSearchParams(window.location.search);
        
        // Load filters from URL
        if (params.has('search')) {
            this.filters.search = params.get('search');
            document.getElementById('searchInput').value = this.filters.search;
        }
        
        if (params.has('minPrice')) {
            this.filters.minPrice = params.get('minPrice');
            document.getElementById('minPrice').value = this.filters.minPrice;
        }
        
        if (params.has('maxPrice')) {
            this.filters.maxPrice = params.get('maxPrice');
            document.getElementById('maxPrice').value = this.filters.maxPrice;
        }
        
        if (params.has('brands')) {
            this.filters.brands = params.get('brands').split(',');
        }
        
        if (params.has('categories')) {
            this.filters.categories = params.get('categories').split(',');
        }
        
        if (params.has('stockStatus')) {
            const stockStatus = params.get('stockStatus');
            // Handle removed 'outOfStock' option
            if (stockStatus === 'outOfStock') {
                this.filters.stockStatus = 'all';
            } else {
                this.filters.stockStatus = stockStatus;
            }
            document.querySelector(`input[name="stockStatus"][value="${this.filters.stockStatus}"]`).checked = true;
        }
        
        if (params.has('sort')) {
            this.sortBy = params.get('sort');
            document.getElementById('sortSelect').value = this.sortBy;
        }
        
        if (params.has('itemsPerPage')) {
            this.itemsPerPage = params.get('itemsPerPage') === 'all' ? 'all' : parseInt(params.get('itemsPerPage'));
            document.getElementById('itemsPerPage').value = this.itemsPerPage;
        }
        
        if (params.has('page')) {
            this.currentPage = parseInt(params.get('page'));
        }
    }

    saveUserPreferences() {
        const preferences = {
            sortBy: this.sortBy,
            itemsPerPage: this.itemsPerPage
        };
        
        try {
            // Note: localStorage is not available in sandbox, but keeping for completeness
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem('hockey-inventory-preferences', JSON.stringify(preferences));
            }
        } catch (e) {
            console.warn('Could not save preferences to localStorage:', e);
        }
    }

    loadUserPreferences() {
        try {
            if (typeof localStorage !== 'undefined') {
                const saved = localStorage.getItem('hockey-inventory-preferences');
                if (saved) {
                    const preferences = JSON.parse(saved);
                    this.sortBy = preferences.sortBy || 'name-asc';
                    this.itemsPerPage = preferences.itemsPerPage || 24;
                    
                    // Update form controls
                    document.getElementById('sortSelect').value = this.sortBy;
                    document.getElementById('itemsPerPage').value = this.itemsPerPage;
                }
            }
        } catch (e) {
            console.warn('Could not load preferences from localStorage:', e);
        }
    }

    showError(message) {
        const loadingState = document.getElementById('loadingState');
        loadingState.innerHTML = `
            <div style="text-align: center; color: var(--color-error);">
                <h3>Error</h3>
                <p>${message}</p>
                <button class="btn btn--primary" onclick="window.location.reload()">Retry</button>
            </div>
        `;
    }

    // Cart Management Methods
    setupCartEventListeners() {
        // Cart button to open/close cart
        document.getElementById('cartButton').addEventListener('click', () => {
            this.toggleCart();
        });

        // Cart close button
        document.getElementById('cartClose').addEventListener('click', () => {
            this.closeCart();
        });

        // Cart overlay to close cart
        document.getElementById('cartOverlay').addEventListener('click', () => {
            this.closeCart();
        });

        // Clear cart button
        document.getElementById('cartClear').addEventListener('click', () => {
            this.clearCart();
        });

        // Checkout button (placeholder)
        document.getElementById('cartCheckout').addEventListener('click', () => {
            this.checkout();
        });

        // Escape key to close cart
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isCartOpen) {
                this.closeCart();
            }
        });
    }

    /**
     * Add an item to the cart
     * @param {number} itemId - The ID of the item to add
     */
    addToCart(itemId) {
        const item = this.inventory.find(i => i.id === itemId);
        if (!item || item.stock === 0) {
            return;
        }

        const existingCartItem = this.cart.find(cartItem => cartItem.id === itemId);
        
        if (existingCartItem) {
            // Check if we can add more (don't exceed stock)
            if (existingCartItem.quantity < item.stock) {
                existingCartItem.quantity += 1;
            } else {
                this.showNotification('Cannot add more items than available in stock', 'warning');
                return;
            }
        } else {
            // Add new item to cart
            this.cart.push({
                id: item.id,
                name: item.name,
                brand: item.brand,
                category: item.category,
                price: item.price,
                priceNumeric: item.priceNumeric,
                quantity: 1,
                maxStock: item.stock
            });
        }

        this.updateCartUI();
        this.saveCartToStorage();
        this.showNotification(`${item.name} added to cart`, 'success');
    }

    /**
     * Remove an item from the cart
     * @param {number} itemId - The ID of the item to remove
     */
    removeFromCart(itemId) {
        this.cart = this.cart.filter(item => item.id !== itemId);
        this.updateCartUI();
        this.saveCartToStorage();
    }

    /**
     * Update the quantity of an item in the cart
     * @param {number} itemId - The ID of the item
     * @param {number} newQuantity - The new quantity
     */
    updateCartItemQuantity(itemId, newQuantity) {
        const cartItem = this.cart.find(item => item.id === itemId);
        if (!cartItem) return;

        if (newQuantity <= 0) {
            this.removeFromCart(itemId);
        } else if (newQuantity <= cartItem.maxStock) {
            cartItem.quantity = newQuantity;
            this.updateCartUI();
            this.saveCartToStorage();
        } else {
            this.showNotification('Cannot add more items than available in stock', 'warning');
        }
    }

    /**
     * Clear all items from the cart
     */
    clearCart() {
        if (this.cart.length === 0) return;
        
        if (confirm('Are you sure you want to clear your cart?')) {
            this.cart = [];
            this.updateCartUI();
            this.saveCartToStorage();
            this.showNotification('Cart cleared', 'info');
        }
    }

    /**
     * Toggle cart sidebar visibility
     */
    toggleCart() {
        if (this.isCartOpen) {
            this.closeCart();
        } else {
            this.openCart();
        }
    }

    /**
     * Open the cart sidebar
     */
    openCart() {
        // Store the currently focused element
        this.lastFocusedElement = document.activeElement;
        
        this.isCartOpen = true;
        document.getElementById('cartOverlay').classList.add('active');
        document.getElementById('cartSidebar').classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Focus the cart sidebar for accessibility
        const cartSidebar = document.getElementById('cartSidebar');
        if (cartSidebar) {
            cartSidebar.focus();
        }
    }

    /**
     * Close the cart sidebar
     */
    closeCart() {
        this.isCartOpen = false;
        document.getElementById('cartOverlay').classList.remove('active');
        document.getElementById('cartSidebar').classList.remove('active');
        document.body.style.overflow = '';
        
        // Restore focus to the previously focused element
        if (this.lastFocusedElement) {
            this.lastFocusedElement.focus();
            this.lastFocusedElement = null;
        }
    }

    /**
     * Update the cart UI elements
     */
    updateCartUI() {
        const cartCount = document.getElementById('cartCount');
        const cartEmpty = document.getElementById('cartEmpty');
        const cartItems = document.getElementById('cartItems');
        const cartFooter = document.getElementById('cartFooter');
        const cartSubtotal = document.getElementById('cartSubtotal');
        const cartTotal = document.getElementById('cartTotal');

        // Update cart count
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;

        // Show/hide empty state
        if (this.cart.length === 0) {
            cartEmpty.style.display = 'flex';
            cartItems.classList.remove('has-items');
            cartFooter.style.display = 'none';
        } else {
            cartEmpty.style.display = 'none';
            cartItems.classList.add('has-items');
            cartFooter.style.display = 'block';
        }

        // Render cart items
        cartItems.innerHTML = this.cart.map(item => `
            <div class="cart-item">
                <div class="cart-item-info">
                    <h4 class="cart-item-name">${item.name}</h4>
                    <div class="cart-item-details">
                        <span class="cart-item-brand">${item.brand}</span>
                        <span class="cart-item-category">${item.category}</span>
                    </div>
                    <div class="cart-item-price">${item.price}</div>
                </div>
                <div class="cart-item-controls">
                    <div class="cart-item-quantity">
                        <button onclick="app.updateCartItemQuantity(${item.id}, ${item.quantity - 1})" 
                                ${item.quantity <= 1 ? 'disabled' : ''}>
                            −
                        </button>
                        <span>${item.quantity}</span>
                        <button onclick="app.updateCartItemQuantity(${item.id}, ${item.quantity + 1})" 
                                ${item.quantity >= item.maxStock ? 'disabled' : ''}>
                            +
                        </button>
                    </div>
                    <button class="cart-item-remove" onclick="app.removeFromCart(${item.id})">
                        Remove
                    </button>
                </div>
            </div>
        `).join('');

        // Calculate and update totals
        const subtotal = this.cart.reduce((sum, item) => sum + (item.priceNumeric * item.quantity), 0);
        cartSubtotal.textContent = `$${subtotal.toFixed(2)}`;
        cartTotal.textContent = `$${subtotal.toFixed(2)}`; // For now, total equals subtotal
    }

    /**
     * Save cart to localStorage
     */
    saveCartToStorage() {
        try {
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem('hockey-inventory-cart', JSON.stringify(this.cart));
            }
        } catch (e) {
            console.warn('Could not save cart to localStorage:', e);
        }
    }

    /**
     * Load cart from localStorage
     */
    loadCartFromStorage() {
        try {
            if (typeof localStorage !== 'undefined') {
                const saved = localStorage.getItem('hockey-inventory-cart');
                if (saved) {
                    this.cart = JSON.parse(saved);
                    // Validate cart items against current inventory
                    this.validateCart();
                }
            }
        } catch (e) {
            console.warn('Could not load cart from localStorage:', e);
            this.cart = [];
        }
    }

    /**
     * Validate cart items against current inventory
     */
    validateCart() {
        this.cart = this.cart.filter(cartItem => {
            const inventoryItem = this.inventory.find(item => item.id === cartItem.id);
            if (!inventoryItem) {
                return false; // Remove items that no longer exist
            }
            
            // Update max stock and adjust quantity if needed
            cartItem.maxStock = inventoryItem.stock;
            if (cartItem.quantity > inventoryItem.stock) {
                cartItem.quantity = Math.max(0, inventoryItem.stock);
            }
            
            return cartItem.quantity > 0;
        });
    }

    /**
     * Show a notification to the user
     * @param {string} message - The message to show
     * @param {string} type - The type of notification (success, error, warning, info)
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification--${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="notification-close">×</button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);

        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });

        // Trigger animation
        setTimeout(() => {
            notification.classList.add('notification--show');
        }, 10);
    }

    /**
     * Handle checkout process - redirect to cart summary page
     */
    checkout() {
        if (this.cart.length === 0) {
            this.showNotification('Your cart is empty', 'warning');
            return;
        }

        // Close the cart sidebar before navigating
        this.closeCart();
        
        // Navigate to cart summary page
        window.location.href = 'cart-summary.html';
    }

    /**
     * Setup navigation event listeners
     */
    setupNavigationEventListeners() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mainNav = document.getElementById('mainNav');
        const navOverlay = document.createElement('div');
        navOverlay.className = 'nav-overlay';
        document.body.appendChild(navOverlay);

        // Mobile menu toggle
        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => {
                const isOpen = mainNav.classList.contains('active');
                
                if (isOpen) {
                    this.closeMobileMenu();
                } else {
                    this.openMobileMenu();
                }
            });
        }

        // Close mobile menu when clicking overlay
        navOverlay.addEventListener('click', () => {
            this.closeMobileMenu();
        });

        // Close mobile menu on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && mainNav.classList.contains('active')) {
                this.closeMobileMenu();
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && mainNav.classList.contains('active')) {
                this.closeMobileMenu();
            }
        });
    }

    /**
     * Open mobile menu
     */
    openMobileMenu() {
        const mainNav = document.getElementById('mainNav');
        const navOverlay = document.querySelector('.nav-overlay');
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        
        mainNav.classList.add('active');
        navOverlay.classList.add('active');
        mobileMenuToggle.setAttribute('aria-expanded', 'true');
        document.body.style.overflow = 'hidden';
        
        // Focus first nav link for accessibility
        const firstNavLink = mainNav.querySelector('a');
        if (firstNavLink) {
            firstNavLink.focus();
        }
    }

    /**
     * Close mobile menu
     */
    closeMobileMenu() {
        const mainNav = document.getElementById('mainNav');
        const navOverlay = document.querySelector('.nav-overlay');
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        
        mainNav.classList.remove('active');
        navOverlay.classList.remove('active');
        mobileMenuToggle.setAttribute('aria-expanded', 'false');
        document.body.style.overflow = '';
    }

    /**
     * Initialize light mode only (theme toggle functionality removed)
     */
    initializeLightMode() {
        // Force light mode by setting the data attribute
        document.documentElement.setAttribute('data-color-scheme', 'light');
    }

    /**
     * Initialize accessibility features
     */
    initializeAccessibilityFeatures() {
        // Add skip link functionality
        const skipLink = document.querySelector('.skip-link');
        if (skipLink) {
            skipLink.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.getElementById('main-content');
                if (target) {
                    target.focus();
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }

        // Enhance keyboard navigation for dropdowns
        this.setupDropdownKeyboardNavigation();
        
        // Add live region for dynamic content updates
        this.createLiveRegion();
        
        // Setup focus management for modals
        this.setupModalFocusManagement();
    }

    /**
     * Setup keyboard navigation for dropdown menus
     */
    setupDropdownKeyboardNavigation() {
        const dropdowns = document.querySelectorAll('.dropdown');
        
        dropdowns.forEach(dropdown => {
            const button = dropdown.querySelector('.dropdown-button');
            const menu = dropdown.querySelector('.dropdown-menu');
            
            if (button && menu) {
                button.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowDown' || e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        dropdown.classList.add('active');
                        const firstOption = menu.querySelector('input, button');
                        if (firstOption) {
                            firstOption.focus();
                        }
                    }
                });

                menu.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        dropdown.classList.remove('active');
                        button.focus();
                    }
                });
            }
        });
    }

    /**
     * Create live region for screen reader announcements
     */
    createLiveRegion() {
        if (!document.getElementById('live-region')) {
            const liveRegion = document.createElement('div');
            liveRegion.id = 'live-region';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.style.position = 'absolute';
            liveRegion.style.left = '-10000px';
            liveRegion.style.width = '1px';
            liveRegion.style.height = '1px';
            liveRegion.style.overflow = 'hidden';
            document.body.appendChild(liveRegion);
        }
    }

    /**
     * Announce message to screen readers
     * @param {string} message - Message to announce
     */
    announceToScreenReader(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
            // Clear after announcement
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    }

    /**
     * Setup focus management for modal dialogs
     */
    setupModalFocusManagement() {
        // Store the element that had focus before opening modal
        this.lastFocusedElement = null;
        
        // Setup focus trap for cart sidebar
        const cartSidebar = document.getElementById('cartSidebar');
        if (cartSidebar) {
            cartSidebar.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    this.trapFocus(e, cartSidebar);
                }
            });
        }
    }

    /**
     * Trap focus within a container
     * @param {Event} e - Keyboard event
     * @param {Element} container - Container to trap focus within
     */
    trapFocus(e, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }
}

// Global functions for event handlers
window.clearAllFilters = function() {
    if (window.app) {
        window.app.clearAllFilters();
    }
};

window.goToPage = function(page) {
    if (window.app) {
        window.app.goToPage(page);
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new InventoryApp();
});
