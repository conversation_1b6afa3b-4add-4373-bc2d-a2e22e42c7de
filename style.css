
:root {
  /* High Contrast Colors - Light Mode Only - WCAG AAA Compliant */
  --color-background: rgba(255, 255, 255, 1); /* Pure white background */
  --color-surface: rgba(248, 248, 248, 1); /* Very light gray for surfaces */
  --color-text: rgba(0, 0, 0, 1); /* Pure black text - 21:1 contrast ratio */
  --color-text-secondary: rgba(51, 51, 51, 1); /* Dark gray - 12.63:1 contrast ratio */
  --color-primary: rgba(0, 102, 204, 1); /* Dark blue - high contrast */
  --color-primary-hover: rgba(0, 85, 170, 1); /* Darker blue on hover */
  --color-primary-active: rgba(0, 68, 136, 1); /* Even darker blue when active */
  --color-secondary: rgba(221, 221, 221, 1); /* Light gray for secondary elements */
  --color-secondary-hover: rgba(204, 204, 204, 1); /* Darker gray on hover */
  --color-secondary-active: rgba(187, 187, 187, 1); /* Even darker gray when active */
  --color-border: rgba(119, 119, 119, 1); /* Dark gray borders - 4.54:1 contrast */
  --color-btn-primary-text: rgba(255, 255, 255, 1); /* White text on dark buttons */
  --color-card-border: rgba(153, 153, 153, 1); /* Visible card borders */
  --color-card-border-inner: rgba(170, 170, 170, 1); /* Inner card borders */
  --color-error: rgba(170, 0, 0, 1); /* Dark red for errors */
  --color-success: rgba(0, 119, 0, 1); /* Dark green for success */
  --color-warning: rgba(153, 102, 0, 1); /* Dark orange for warnings */
  --color-info: rgba(0, 85, 170, 1); /* Dark blue for info */
  --color-focus-ring: rgba(0, 102, 204, 0.8); /* High contrast focus ring */
  --color-select-caret: rgba(0, 0, 0, 1); /* Black caret for visibility */

  /* Common style patterns - Light Mode Only */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for light mode - High Contrast */
  --color-success-rgb: 0, 119, 0;
  --color-error-rgb: 170, 0, 0;
  --color-warning-rgb: 153, 102, 0;
  --color-info-rgb: 0, 85, 170;

  /* Typography */
  --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border radius */
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --ease-standard: cubic-bezier(0.4, 0.0, 0.2, 1);
  --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Base styles */
html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 var(--space-4) 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
  margin: 0 0 var(--space-4) 0;
  color: var(--color-text);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

a:focus {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

code, pre {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
}

code {
  padding: var(--space-1) var(--space-2);
  background: var(--color-secondary);
  border-radius: var(--radius-sm);
  color: var(--color-text);
}

pre {
  padding: var(--space-4);
  background: var(--color-surface);
  border-radius: var(--radius-md);
  overflow-x: auto;
  border: 1px solid var(--color-border);
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  user-select: none;
  white-space: nowrap;
}

.btn:focus {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border-color: var(--color-primary);
}

.btn--primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn--primary:active:not(:disabled) {
  background: var(--color-primary-active);
  border-color: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
  border-color: var(--color-secondary);
}

.btn--secondary:hover:not(:disabled) {
  background: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
}

.btn--secondary:active:not(:disabled) {
  background: var(--color-secondary-active);
  border-color: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn--outline:hover:not(:disabled) {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--outline:active:not(:disabled) {
  background: var(--color-primary-active);
  border-color: var(--color-primary-active);
}

/* Button sizes */
.btn--sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-sm);
}

.btn--lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-lg);
}

/* Form controls */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

.form-control::placeholder {
  color: var(--color-text-secondary);
  opacity: 1;
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

select.form-control {
  background-image: var(--select-caret);
  background-repeat: no-repeat;
  background-position: right var(--space-3) center;
  background-size: 16px;
  padding-right: var(--space-10);
  appearance: none;
}

/* Header */
.header {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
  gap: var(--space-4);
}

.header-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.header-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--color-text);
  text-decoration: none;
}

.brand-link:hover {
  color: var(--color-primary);
  text-decoration: none;
}

.brand-icon {
  flex-shrink: 0;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 3px;
  padding: var(--space-2);
  background: none;
  border: none;
  cursor: pointer;
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
}

/* Navigation */
.main-nav {
  flex: 1;
}

.nav-list {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: 0;
  padding: 0;
  list-style: none;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--space-2) var(--space-4);
  color: var(--color-text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-base);
  transition: all var(--duration-fast) var(--ease-standard);
  position: relative;
}

.nav-link:hover {
  color: var(--color-primary);
  background: var(--color-secondary);
}

.nav-link.active {
  color: var(--color-primary);
  background: rgba(var(--color-primary), 0.1);
}

.nav-link:focus {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.header-counter {
  padding: var(--space-2) var(--space-3);
  background: var(--color-secondary);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.cart-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  position: relative;
}

.cart-button:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
}

.cart-button:focus {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

.cart-count {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 var(--space-1);
  background: var(--color-background);
  color: var(--color-primary);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  line-height: 1;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Main content */
.main-content {
  min-height: calc(100vh - 80px);
  padding: var(--space-6) 0;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--space-8);
  align-items: start;
}

/* Sidebar */
.sidebar {
  position: sticky;
  top: calc(80px + var(--space-6));
  max-height: calc(100vh - 80px - var(--space-12));
  overflow-y: auto;
}

.filters-container {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
}

.filters-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--color-border);
}

.filters-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.filter-group {
  margin-bottom: var(--space-6);
}

.filter-group:last-child {
  margin-bottom: 0;
}

/* Price Range Inputs */
.price-inputs {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.price-input {
  flex: 1;
  min-width: 0;
}

.price-separator {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

/* Dropdown Filter */
.dropdown-filter {
  position: relative;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  color: var(--color-text);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.dropdown-toggle:hover {
  border-color: var(--color-primary);
}

.dropdown-toggle:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

.dropdown-arrow {
  transition: transform var(--duration-fast) var(--ease-standard);
}

.dropdown-filter.open .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 50;
  margin-top: var(--space-1);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all var(--duration-fast) var(--ease-standard);
}

.dropdown-filter.open .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-search {
  padding: var(--space-3);
  border-bottom: 1px solid var(--color-border);
}

.dropdown-options {
  max-height: 200px;
  overflow-y: auto;
  padding: var(--space-2) 0;
}

.dropdown-option {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  cursor: pointer;
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.dropdown-option:hover {
  background: var(--color-secondary);
}

.dropdown-option input[type="checkbox"] {
  margin: 0;
}

/* Radio Group */
.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.radio-option {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-base);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.radio-option:hover {
  background: var(--color-secondary);
}

.radio-option input[type="radio"] {
  margin: 0;
}

/* Content Area */
.content-area {
  min-width: 0;
}

.content-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  gap: var(--space-4);
}

.results-info {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.controls-group {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.control-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.control-item .form-label {
  margin-bottom: 0;
  white-space: nowrap;
}

.control-item .form-control {
  width: auto;
  min-width: 120px;
}

/* Loading and No Results States */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16) var(--space-4);
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-secondary);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-results {
  text-align: center;
  padding: var(--space-16) var(--space-4);
}

.no-results p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
}

/* Inventory Grid */
.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

/* Inventory Cards */
.inventory-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
  overflow: hidden;
}

.inventory-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.card-header {
  margin-bottom: var(--space-4);
}

.card-title {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  line-height: var(--line-height-tight);
}

.card-badges {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-base);
  line-height: 1;
}

.badge--brand {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-primary);
}

.badge--category {
  background: rgba(var(--color-secondary-rgb, 221, 221, 221), 1);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.card-price {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-2);
}

.card-system-id {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-3);
}

.card-stock {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.stock-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-base);
  line-height: 1;
  border: 1px solid;
}

.stock-badge--in-stock {
  background: rgba(var(--color-success-rgb), var(--status-bg-opacity));
  color: var(--color-success);
  border-color: rgba(var(--color-success-rgb), var(--status-border-opacity));
}

.stock-badge--low-stock {
  background: rgba(var(--color-warning-rgb), var(--status-bg-opacity));
  color: var(--color-warning);
  border-color: rgba(var(--color-warning-rgb), var(--status-border-opacity));
}

.stock-badge--very-low {
  background: var(--color-warning);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-warning);
}

.stock-badge--out-of-stock {
  background: rgba(var(--color-error-rgb), var(--status-bg-opacity));
  color: var(--color-error);
  border-color: rgba(var(--color-error-rgb), var(--status-border-opacity));
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: var(--space-8);
}

.pagination {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-fast) var(--ease-standard);
}

.pagination-btn:hover {
  background: var(--color-secondary);
  border-color: var(--color-primary);
  color: var(--color-primary);
  text-decoration: none;
}

.pagination-btn.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn:disabled:hover {
  background: var(--color-background);
  border-color: var(--color-border);
  color: var(--color-text);
}

/* Cart Sidebar */
.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.cart-overlay.active {
  opacity: 1;
  visibility: visible;
}

.cart-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 400px;
  background: var(--color-background);
  border-left: 1px solid var(--color-border);
  z-index: 201;
  transform: translateX(100%);
  transition: transform var(--duration-normal) var(--ease-standard);
  display: flex;
  flex-direction: column;
}

.cart-sidebar.active {
  transform: translateX(0);
}

.cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-border);
}

.cart-header h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.cart-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  border-radius: var(--radius-base);
  transition: all var(--duration-fast) var(--ease-standard);
}

.cart-close:hover {
  background: var(--color-secondary);
  color: var(--color-text);
}

.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6);
}

.cart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--space-16) var(--space-4);
}

.cart-empty-icon {
  margin-bottom: var(--space-4);
  color: var(--color-text-secondary);
}

.cart-empty p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
}

.cart-empty-subtitle {
  font-size: var(--font-size-sm);
}

.cart-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--color-border);
}

.cart-total {
  margin-bottom: var(--space-4);
}

.cart-total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.cart-total-final {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  padding-top: var(--space-2);
  border-top: 1px solid var(--color-border);
}

.cart-actions {
  display: flex;
  gap: var(--space-3);
}

.cart-clear {
  flex: 1;
}

.cart-checkout {
  flex: 2;
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-base);
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* High contrast hockey theme colors for accessibility */
:root {
  --hockey-navy: rgba(0, 51, 102, 1); /* Dark navy blue */
  --hockey-ice-blue: rgba(173, 216, 230, 1); /* Light blue */
  --hockey-light-blue: rgba(135, 206, 235, 1); /* Sky blue */
  --hockey-accent: rgba(255, 69, 0, 1); /* Orange red accent */
}

/* Hockey Equipment Inventory Specific Styles */
.header {
  background: var(--hockey-navy); /* Solid background for high contrast */
}

.header-counter {
  background: rgba(var(--hockey-ice-blue), 0.2);
  color: var(--color-text);
  border: 1px solid rgba(var(--hockey-ice-blue), 0.3);
}

/* Main layout adjustments */
.main-content {
  background: var(--color-background);
}

.sidebar {
  background: var(--color-surface);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    max-width: 100%;
    padding: 0 var(--space-3);
  }
  
  .content-wrapper {
    grid-template-columns: 240px 1fr;
    gap: var(--space-6);
  }
  
  .sidebar {
    position: static;
    max-height: none;
  }
}

@media (max-width: 768px) {
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .sidebar {
    order: 2;
  }
  
  .content-area {
    order: 1;
  }
  
  .header-content {
    padding: var(--space-3) 0;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .main-nav {
    display: none;
  }
  
  .header-actions {
    gap: var(--space-2);
  }
  
  .cart-sidebar {
    width: 100%;
    max-width: 400px;
  }
  
  .inventory-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--space-4);
  }
  
  .content-controls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }
  
  .controls-group {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .control-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-1);
  }
  
  .control-item .form-control {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-2);
  }
  
  .header-content {
    gap: var(--space-2);
  }
  
  .header-title {
    font-size: var(--font-size-lg);
  }
  
  .brand-link {
    gap: var(--space-1);
  }
  
  .brand-icon {
    width: 24px;
    height: 24px;
  }
  
  .inventory-grid {
    grid-template-columns: 1fr;
  }
  
  .inventory-card {
    padding: var(--space-4);
  }
  
  .filters-container {
    padding: var(--space-4);
  }
  
  .cart-sidebar {
    width: 100vw;
  }
}

/* High contrast mode support - Enhanced */
@media (prefers-contrast: high) {
  :root {
    --color-border: #000000; /* Pure black borders */
    --color-text-secondary: #000000; /* Pure black secondary text */
    --color-background: #ffffff; /* Pure white background */
    --color-surface: #ffffff; /* Pure white surfaces */
    --color-text: #000000; /* Pure black text */
  }
  
  .inventory-card {
    border-width: 2px;
    border-color: #000000;
  }
  
  .btn {
    border-width: 2px;
    border-color: #000000;
  }
  
  .form-control {
    border-width: 2px;
    border-color: #000000;
  }
  
  /* Ensure all interactive elements have strong borders */
  .dropdown-toggle,
  .nav-link,
  .card {
    border-width: 2px;
    border-color: #000000;
  }
}

/* Print styles */
@media print {
  .header,
  .sidebar,
  .cart-sidebar,
  .cart-overlay {
    display: none;
  }
  
  .content-wrapper {
    grid-template-columns: 1fr;
  }
  
  .inventory-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
}
