<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Hockey equipment cart summary and review page">
    <title>Purchase Summary | Hockey Equipment Inventory</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* Cart Summary Specific Styles */
        .cart-summary-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: var(--card-bg);
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
        }
        
        .cart-summary-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }
        
        .cart-summary-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .cart-summary-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }
        
        .cart-summary-items {
            margin-bottom: 2rem;
        }
        
        .cart-summary-item {
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            align-items: center;
        }
        
        .cart-summary-item:last-child {
            border-bottom: none;
        }
        
        .item-details {
            display: flex;
            flex-direction: column;
        }
        
        .item-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }
        
        .item-brand {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .item-quantity {
            font-weight: 600;
            color: var(--text-primary);
            text-align: center;
        }
        
        .item-price {
            font-weight: 600;
            color: var(--text-primary);
            text-align: right;
        }
        
        .item-total {
            font-weight: 700;
            color: var(--primary-color);
            text-align: right;
        }
        
        .cart-summary-total {
            background: var(--bg-secondary);
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .total-row:last-child {
            margin-bottom: 0;
            padding-top: 0.5rem;
            border-top: 2px solid var(--border-color);
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .total-label {
            color: var(--text-primary);
        }
        
        .total-amount {
            color: var(--primary-color);
            font-weight: 700;
        }
        
        .cart-summary-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .copy-success {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: var(--success-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        
        .copy-success.show {
            transform: translateX(0);
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            margin-bottom: 2rem;
            transition: color 0.2s ease;
        }
        
        .back-link:hover {
            color: var(--primary-hover);
        }
        
        .back-icon {
            width: 20px;
            height: 20px;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .cart-summary-container {
                margin: 1rem;
                padding: 1rem;
            }
            
            .cart-summary-item {
                grid-template-columns: 1fr;
                gap: 0.5rem;
                text-align: left;
            }
            
            .item-quantity,
            .item-price,
            .item-total {
                text-align: left;
            }
            
            .cart-summary-actions {
                flex-direction: column;
            }
            
            .copy-success {
                position: fixed;
                top: 1rem;
                left: 1rem;
                right: 1rem;
                transform: translateY(-100%);
            }
            
            .copy-success.show {
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header (simplified) -->
        <header class="header" role="banner">
            <div class="container">
                <div class="header-content">
                    <div class="header-brand">
                        <h1 class="header-title">
                            <a href="/" class="brand-link" aria-label="Hockey Equipment Inventory - Home">
                                <svg class="brand-icon" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                                    <path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7l-10-5z"></path>
                                    <path d="M12 22s8-4 8-10V7l-8-5-8 5v5c0 6 8 10 8 10z"></path>
                                </svg>
                                Hockey Equipment Inventory
                            </a>
                        </h1>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content" role="main">
            <div class="container">
                <a href="index.html" class="back-link">
                    <svg class="back-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 12H5"></path>
                        <path d="M12 19l-7-7 7-7"></path>
                    </svg>
                    Back to Inventory
                </a>
                
                <div class="cart-summary-container">
                    <div class="cart-summary-header">
                        <h1 class="cart-summary-title">Purchase Summary</h1>
                        <p class="cart-summary-subtitle">Review your selected items below</p>
                    </div>
                    
                    <div class="cart-summary-items" id="cartSummaryItems">
                        <!-- Cart items will be populated by JavaScript -->
                    </div>
                    
                    <div class="cart-summary-total" id="cartSummaryTotal">
                        <!-- Total will be populated by JavaScript -->
                    </div>
                    
                    <div class="cart-summary-actions">
                        <button class="btn btn--primary" id="copyListBtn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 0.5rem;">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                            </svg>
                            Copy List
                        </button>
                        <button class="btn btn--outline" onclick="window.location.href='index.html'">
                            Continue Shopping
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Copy Success Notification -->
    <div class="copy-success" id="copySuccess">
        ✓ Cart list copied to clipboard!
    </div>
    
    <script>
        /**
         * Cart Summary Page JavaScript
         * Handles displaying cart items and copy functionality
         */
        class CartSummary {
            constructor() {
                this.cart = [];
                this.inventory = []; // Added to store inventory data
            }
            
            /**
             * Initialize the cart summary page
             */
            async init() {
                await this.loadInventoryData();
                this.loadCartFromStorage();
                this.validateCart();
                this.renderCartSummary();
                this.setupEventListeners();
            }
            
            /**
             * Load inventory data from JSON file
             */
            async loadInventoryData() {
                try {
                    const response = await fetch('inventory_data.json');
                    const data = await response.json();
                    this.inventory = data.inventory || [];
                } catch (error) {
                    console.error('Error loading inventory data:', error);
                    this.inventory = [];
                }
            }
            
            /**
             * Validate cart items against current inventory
             */
            validateCart() {
                this.cart = this.cart.filter(cartItem => {
                    const inventoryItem = this.inventory.find(item => item.id === cartItem.id);
                    if (!inventoryItem) {
                        return false; // Remove items that no longer exist
                    }
                    
                    // If the item is out of stock, remove it
                    if (inventoryItem.stock <= 0) {
                        return false;
                    }
                    
                    // Adjust quantity to available stock
                    if (cartItem.quantity > inventoryItem.stock) {
                        cartItem.quantity = inventoryItem.stock;
                    }
                    
                    return true;
                });
            }
            
            /**
             * Load cart data from localStorage
             */
            loadCartFromStorage() {
                try {
                    if (typeof localStorage !== 'undefined') {
                        const saved = localStorage.getItem('hockey-inventory-cart');
                        if (saved) {
                            this.cart = JSON.parse(saved);
                        }
                    }
                } catch (e) {
                    console.warn('Could not load cart from localStorage:', e);
                    this.cart = [];
                }
            }
            
            /**
             * Render the cart summary display
             */
            renderCartSummary() {
                const itemsContainer = document.getElementById('cartSummaryItems');
                const totalContainer = document.getElementById('cartSummaryTotal');
                
                if (this.cart.length === 0) {
                    itemsContainer.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">No items in cart</p>';
                    totalContainer.innerHTML = '';
                    return;
                }
                
                // Render items
                let itemsHTML = '';
                this.cart.forEach(item => {
                    const lineTotal = item.priceNumeric * item.quantity;
                    itemsHTML += `
                        <div class="cart-summary-item">
                            <div class="item-details">
                                <div class="item-name">${this.escapeHtml(item.name)}</div>
                                <div class="item-brand">${this.escapeHtml(item.brand)}</div>
                            </div>
                            <div class="item-quantity">Qty: ${item.quantity}</div>
                            <div class="item-price">${item.price}</div>
                            <div class="item-total">$${lineTotal.toFixed(2)}</div>
                        </div>
                    `;
                });
                itemsContainer.innerHTML = itemsHTML;
                
                // Calculate and render totals
                const subtotal = this.cart.reduce((sum, item) => sum + (item.priceNumeric * item.quantity), 0);
                const itemCount = this.cart.reduce((sum, item) => sum + item.quantity, 0);
                
                totalContainer.innerHTML = `
                    <div class="total-row">
                        <span class="total-label">Items (${itemCount}):</span>
                        <span class="total-amount">$${subtotal.toFixed(2)}</span>
                    </div>
                    <div class="total-row">
                        <span class="total-label">Total:</span>
                        <span class="total-amount">$${subtotal.toFixed(2)}</span>
                    </div>
                `;
            }
            
            /**
             * Setup event listeners
             */
            setupEventListeners() {
                const copyBtn = document.getElementById('copyListBtn');
                if (copyBtn) {
                    copyBtn.addEventListener('click', () => this.copyCartToClipboard());
                }
            }
            
            /**
             * Copy formatted cart contents to clipboard
             */
            async copyCartToClipboard() {
                if (this.cart.length === 0) {
                    this.showNotification('Cart is empty', 'warning');
                    return;
                }
                
                // Format cart contents for sharing
                let cartText = 'HOCKEY EQUIPMENT ORDER REQUEST\n';
                cartText += '================================\n\n';
                
                this.cart.forEach((item, index) => {
                    const lineTotal = item.priceNumeric * item.quantity;
                    cartText += `${index + 1}. ${item.name}\n`;
                    cartText += `   Brand: ${item.brand}\n`;
                    cartText += `   Quantity: ${item.quantity}\n`;
                    cartText += `   Unit Price: ${item.price}\n`;
                    cartText += `   Line Total: $${lineTotal.toFixed(2)}\n\n`;
                });
                
                const subtotal = this.cart.reduce((sum, item) => sum + (item.priceNumeric * item.quantity), 0);
                const itemCount = this.cart.reduce((sum, item) => sum + item.quantity, 0);
                
                cartText += '================================\n';
                cartText += `Total Items: ${itemCount}\n`;
                cartText += `Total Amount: $${subtotal.toFixed(2)}\n`;
                cartText += '================================\n\n';
                cartText += 'Please confirm availability and provide payment instructions.';
                
                try {
                    await navigator.clipboard.writeText(cartText);
                    this.showCopySuccess();
                } catch (err) {
                    // Fallback for older browsers
                    this.fallbackCopyToClipboard(cartText);
                }
            }
            
            /**
             * Fallback copy method for older browsers
             */
            fallbackCopyToClipboard(text) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                try {
                    document.execCommand('copy');
                    this.showCopySuccess();
                } catch (err) {
                    console.error('Failed to copy text: ', err);
                    alert('Failed to copy to clipboard. Please manually select and copy the cart contents.');
                } finally {
                    document.body.removeChild(textArea);
                }
            }
            
            /**
             * Show copy success notification
             */
            showCopySuccess() {
                const notification = document.getElementById('copySuccess');
                notification.classList.add('show');
                
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
            
            /**
             * Escape HTML to prevent XSS
             */
            escapeHtml(text) {
                const map = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#039;'
                };
                return text.replace(/[&<>"']/g, m => map[m]);
            }
        }
        
        // Initialize cart summary when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new CartSummary();
        });
    </script>
</body>
</html>
