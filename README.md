# Hockey Equipment Inventory Browser

This project is a comprehensive, client-side web application for browsing and managing a large inventory of hockey equipment. It provides a fast, responsive, and user-friendly interface for searching, filtering, and viewing detailed information about each product.

## Key Features

*   **Dynamic Inventory Loading:** Asynchronously fetches inventory data from a remote JSON file, with a fallback to local sample data if the remote source is unavailable.
*   **Advanced Filtering:**
    *   **Text Search:** Real-time search by product name.
    *   **Price Range:** Filter items by minimum and maximum price.
    *   **Brand & Category:** Multi-select dropdowns with search functionality for brands and categories.
    *   **Stock Status:** Filter by "In Stock," "Low Stock," or "Out of Stock."
*   **Sorting Options:** Sort the inventory by name (A-Z, Z-A), price (low-high, high-low), or stock level.
*   **Pagination:** Efficiently handles large datasets with a "show more" pagination system, including an "items per page" selector.
*   **Stateful URLs:** All filter and sort settings are stored in the URL, allowing users to share or bookmark their specific views.
*   **User Preferences:** Remembers the user's sorting and items-per-page preferences using `localStorage`.
*   **Responsive Design:** A clean, modern UI that adapts to various screen sizes, from desktops to mobile devices.
*   **Keyboard Shortcuts:** Includes shortcuts like `Ctrl+F` for search and `Esc` to clear the search input.

## Getting Started

To run this project locally, simply open the `index.html` file in a modern web browser. The project does not require a web server and relies on the local `inventory_data.json` file for data.

## Project Structure

*   `index.html`: The main HTML file that structures the application's layout.
*   `style.css`: Contains all the styles for the application, including a dark mode theme.
*   `app.js`: The core JavaScript file that powers all the application's logic, including data fetching, filtering, sorting, and rendering.
*   `inventory_data.json`: The primary data source, containing a large inventory of hockey equipment.
*   `inventory_data.json`, `sample_inventory_data.json`, `webapp_inventory_data.json`: Additional data files, likely used for different stages of development or as fallbacks.

## Technical Details

*   **Frontend:** Vanilla JavaScript (ES6+), HTML5, CSS3
*   **Data Handling:** Asynchronous `fetch` API for loading JSON data.
*   **State Management:** A single `InventoryApp` class manages the application's state, including the full inventory, filtered items, and user selections.
*   **UI:** The UI is dynamically rendered using JavaScript to create and update the inventory grid, filter options, and pagination.
