<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Professional hockey equipment inventory management system. Browse, filter, and manage hockey gear including sticks, skates, helmets, and protective equipment.">
    <meta name="keywords" content="hockey equipment, inventory, hockey gear, sports equipment, hockey sticks, skates, helmets">
    <meta name="author" content="Hockey Equipment Inventory">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://hockey-inventory.com/">
    <meta property="og:title" content="Hockey Equipment Inventory Browser">
    <meta property="og:description" content="Professional hockey equipment inventory management system">
    <meta property="og:image" content="/assets/og-image.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://hockey-inventory.com/">
    <meta property="twitter:title" content="Hockey Equipment Inventory Browser">
    <meta property="twitter:description" content="Professional hockey equipment inventory management system">
    <meta property="twitter:image" content="/assets/twitter-image.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="style.css" as="style">
    <link rel="preload" href="app.js" as="script">
    
    <title>Hockey Equipment Inventory Browser | Professional Gear Management</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <div class="app-container">
        <!-- Header -->
        <header class="header" role="banner">
            <div class="container">
                <div class="header-content">
                    <!-- Logo and Brand -->
                    <div class="header-brand">
                        <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle navigation menu" aria-expanded="false">
                            <span class="hamburger-line"></span>
                            <span class="hamburger-line"></span>
                            <span class="hamburger-line"></span>
                        </button>
<h1 class="header-title">
    <a href="/" class="brand-link" aria-label="Hockey Equipment Inventory - Home">
        Hockey Equipment Inventory
    </a>
</h1>
                    </div>
                    
                    <!-- Navigation Menu -->
                    <nav class="main-nav" id="mainNav" role="navigation" aria-label="Main navigation">
                        <ul class="nav-list">
                            <li class="nav-item">
                                <a href="#inventory" class="nav-link active" aria-current="page">Inventory</a>
                            </li>
                        </ul>
                    </nav>
                    
                    <!-- Header Actions -->
                    <div class="header-actions">
                        <!-- Item Counter -->
                        <div class="header-counter" aria-live="polite">
                            <span id="itemCounter">Loading...</span>
                        </div>
                        
                        <!-- Cart Button -->
                        <button class="cart-button" id="cartButton" aria-label="Open shopping cart" aria-describedby="cartCount">
                            <svg class="cart-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" aria-hidden="true">
                                <circle cx="9" cy="21" r="1"></circle>
                                <circle cx="20" cy="21" r="1"></circle>
                                <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                            </svg>
                            <span class="cart-text">Cart</span>
                            <span class="cart-count" id="cartCount" aria-label="Items in cart">0</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content" id="main-content" role="main">
            <div class="container">
                <div class="content-wrapper">
                    <!-- Sidebar Filters -->
                    <aside class="sidebar" role="complementary" aria-label="Inventory filters">
                        <div class="filters-container">
                            <div class="filters-header">
                                <h3>Filters</h3>
                                <button class="btn btn--sm btn--outline" id="clearFilters">Clear All</button>
                            </div>

                            <!-- Search Filter -->
                            <div class="filter-group">
                                <label class="form-label" for="searchInput">Search Items</label>
                                <input type="text" id="searchInput" class="form-control" placeholder="Search by name...">
                            </div>

                            <!-- Price Range Filter -->
                            <div class="filter-group">
                                <label class="form-label">Price Range</label>
                                <div class="price-inputs">
                                    <input type="number" id="minPrice" class="form-control price-input" placeholder="Min" min="0" step="0.01">
                                    <span class="price-separator">-</span>
                                    <input type="number" id="maxPrice" class="form-control price-input" placeholder="Max" min="0" step="0.01">
                                </div>
                            </div>

                            <!-- Brand Filter -->
                            <div class="filter-group">
                                <label class="form-label">Brand</label>
                                <div class="dropdown-filter">
                                    <button class="dropdown-toggle" id="brandDropdown">
                                        <span>Select Brands</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                    <div class="dropdown-menu" id="brandMenu">
                                        <div class="dropdown-search">
                                            <input type="text" class="form-control" placeholder="Search brands..." id="brandSearch">
                                        </div>
                                        <div class="dropdown-options" id="brandOptions">
                                            <!-- Brand options will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Category Filter -->
                            <div class="filter-group">
                                <label class="form-label">Category</label>
                                <div class="dropdown-filter">
                                    <button class="dropdown-toggle" id="categoryDropdown">
                                        <span>Select Categories</span>
                                        <span class="dropdown-arrow">▼</span>
                                    </button>
                                    <div class="dropdown-menu" id="categoryMenu">
                                        <div class="dropdown-search">
                                            <input type="text" class="form-control" placeholder="Search categories..." id="categorySearch">
                                        </div>
                                        <div class="dropdown-options" id="categoryOptions">
                                            <!-- Category options will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Stock Status Filter -->
                            <div class="filter-group">
                                <label class="form-label">Stock Status</label>
                                <div class="radio-group">
                                    <label class="radio-option">
                                        <input type="radio" name="stockStatus" value="all" checked>
                                        <span>All Items</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="stockStatus" value="inStock">
                                        <span>In Stock (>0)</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="stockStatus" value="lowStock">
                                        <span>Low Stock (≤5)</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="stockStatus" value="outOfStock">
                                        <span>Out of Stock (0)</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </aside>

                    <!-- Content Area -->
                    <div class="content-area">
                        <!-- Content Controls -->
                        <div class="content-controls">
                            <div class="results-info">
                                <span id="resultsCounter">Showing 0 of 0 items</span>
                            </div>
                            <div class="controls-group">
                                <div class="control-item">
                                    <label class="form-label" for="sortSelect">Sort by:</label>
                                    <select id="sortSelect" class="form-control">
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="price-asc">Price Low-High</option>
                                        <option value="price-desc">Price High-Low</option>
                                        <option value="stock-desc">Stock Level</option>
                                    </select>
                                </div>
                                <div class="control-item">
                                    <label class="form-label" for="itemsPerPage">Items per page:</label>
                                    <select id="itemsPerPage" class="form-control">
                                        <option value="12">12</option>
                                        <option value="24">24</option>
                                        <option value="48">48</option>
                                        <option value="all">All</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Loading State -->
                        <div class="loading-state" id="loadingState">
                            <div class="loading-spinner"></div>
                            <p>Loading inventory...</p>
                        </div>

                        <!-- No Results State -->
                        <div class="no-results" id="noResults" style="display: none;">
                            <p>No items found matching your filters.</p>
                            <button class="btn btn--primary" onclick="clearAllFilters()">Clear All Filters</button>
                        </div>

                        <!-- Inventory Grid -->
                        <div class="inventory-grid" id="inventoryGrid">
                            <!-- Inventory items will be populated by JavaScript -->
                        </div>

                        <!-- Pagination -->
                        <div class="pagination-container" id="paginationContainer">
                            <div class="pagination" id="pagination">
                                <!-- Pagination buttons will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Cart Sidebar -->
    <div class="cart-overlay" id="cartOverlay"></div>
    <div class="cart-sidebar" id="cartSidebar">
        <div class="cart-header">
            <h2>Shopping Cart</h2>
            <button class="cart-close" id="cartClose">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <div class="cart-content">
            <div class="cart-empty" id="cartEmpty">
                <svg class="cart-empty-icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                    <circle cx="9" cy="21" r="1"></circle>
                    <circle cx="20" cy="21" r="1"></circle>
                    <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                </svg>
                <p>Your cart is empty</p>
                <p class="cart-empty-subtitle">Add some hockey equipment to get started!</p>
            </div>
            <div class="cart-items" id="cartItems">
                <!-- Cart items will be populated by JavaScript -->
            </div>
        </div>
        <div class="cart-footer" id="cartFooter">
            <div class="cart-total">
                <div class="cart-total-row">
                    <span>Subtotal:</span>
                    <span id="cartSubtotal">$0.00</span>
                </div>
                <div class="cart-total-row cart-total-final">
                    <span>Total:</span>
                    <span id="cartTotal">$0.00</span>
                </div>
            </div>
            <div class="cart-actions">
                <button class="btn btn--outline cart-clear" id="cartClear">Clear Cart</button>
                <button class="btn btn--primary cart-checkout" id="cartCheckout">Checkout</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
