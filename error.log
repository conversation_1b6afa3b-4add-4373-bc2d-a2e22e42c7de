index.html:1 Access to fetch at 'file:///Users/<USER>/Documents/GitHub/hockey-inventory-website/inventory_data.json' from origin 'null' has been blocked by CORS policy: Cross origin requests are only supported for protocol schemes: chrome, chrome-extension, chrome-untrusted, data, http, https, isolated-app.
inventory_data.json:1  Failed to load resource: net::ERR_FAILED
app.js:59 Error loading inventory data: TypeError: Failed to fetch
    at InventoryApp.loadInventoryData (app.js:50:36)
    at InventoryApp.init (app.js:28:24)
    at new InventoryApp (app.js:23:14)
    at HTMLDocument.<anonymous> (app.js:1250:18)
loadInventoryData @ app.js:59
/favicon.ico:1  Failed to load resource: net::ERR_FILE_NOT_FOUND
